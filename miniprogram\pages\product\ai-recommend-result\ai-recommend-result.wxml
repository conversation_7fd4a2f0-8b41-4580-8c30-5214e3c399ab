<!--pages/product/ai-recommend-result/ai-recommend-result.wxml-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">AI推荐结果</text>
    <text class="page-desc">基于您的需求，为您推荐{{recommendedProducts.length}}款产品</text>

    <!-- 产品对比输入组件 -->
    <product-compare-input
      visible="{{compareVisible}}"
      bind:toggleVisible="onToggleCompareVisible"
    />
  </view>

  <!-- 用户问题信息展示 -->
  <view class="question-summary" wx:if="{{questionInfo.title}}">
    <view class="summary-header">
      <text class="summary-title">您的需求</text>
    </view>
    <view class="summary-content">
      <view class="summary-item">
        <text class="summary-label">需求描述：</text>
        <text class="summary-value">{{questionInfo.title}}</text>
      </view>
      <view class="summary-item" wx:if="{{questionInfo.scene}}">
        <text class="summary-label">使用场景：</text>
        <text class="summary-value">{{questionInfo.scene}}</text>
      </view>
      <view class="summary-item" wx:if="{{questionInfo.keyFactors}}">
        <text class="summary-label">关键因素：</text>
        <text class="summary-value">{{questionInfo.keyFactors}}</text>
      </view>
      <view class="summary-item" wx:if="{{questionInfo.budget && (questionInfo.budget.min || questionInfo.budget.max)}}">
        <text class="summary-label">预算范围：</text>
        <text class="summary-value">
          {{questionInfo.budget.min || '不限'}} - {{questionInfo.budget.max || '不限'}} 元
        </text>
      </view>
      <view class="summary-item" wx:if="{{questionInfo.tags && questionInfo.tags.length > 0}}">
        <text class="summary-label">产品类别：</text>
        <text class="summary-value">{{questionInfo.tags.join('、')}}</text>
      </view>
    </view>
  </view>

  <!-- AI推荐结果展示区域 -->
  <view class="results-container">
    <!-- 推荐产品列表 -->
    <view class="products-list" wx:if="{{recommendedProducts.length > 0}}">
      <view class="product-item" wx:for="{{recommendedProducts}}" wx:key="index">
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>

          <!-- AI推荐理由展示区域 -->
          <view class="ai-recommend-reason" wx:if="{{item.hasRecommendReason}}">
            <view class="reason-header">
              <text class="ai-tag">AI推荐理由</text>
              <text class="expand-btn" bindtap="toggleReasonExpanded" data-index="{{index}}">
                {{item.expanded ? '收起' : '展开'}}
              </text>
            </view>

            <!-- 推荐理由内容 -->
            <view class="reason-content {{item.expanded ? 'expanded' : 'collapsed'}}">
              <!-- 推荐理由 -->
              <view class="recommend-reason" wx:if="{{item.recommendReason}}">
                <text class="reason-text">{{item.recommendReason}}</text>
              </view>

              <!-- 产品优势点 -->
              <view class="highlights-section" wx:if="{{item.highlights && item.highlights.length > 0}}">
                <text class="highlights-title">产品优势：</text>
                <view class="highlights-list">
                  <text class="highlight-item" wx:for="{{item.highlights}}" wx:for-item="highlight" wx:key="*this">
                    • {{highlight}}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="product-actions">
          <button class="action-btn compare-btn {{isInCompareList[item.originalData.skuId] ? 'in-compare' : ''}}" bindtap="addToCompare" data-product="{{item}}">
            <text class="btn-icon">📊</text>
            <text>{{isInCompareList[item.originalData.skuId] ? '移出对比' : '加入对比'}}</text>
          </button>
          <button class="action-btn detail-btn" bindtap="viewDetail" data-product="{{item}}">
            <text class="btn-icon">📋</text>
            <text>详情</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{recommendedProducts.length === 0}}">
      <text class="empty-icon">🤔</text>
      <text class="empty-title">暂无推荐结果</text>
      <text class="empty-desc">请返回重新设置推荐条件</text>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-button secondary-btn" bindtap="reRecommend">
      <text class="btn-icon">🔄</text>
      <text>重新推荐</text>
    </button>
  </view>
</view>